{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_e76d35f6._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_4c46a49c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "JZiPvQ5XzRHs0H9v2y0AQPgZIzl0VWNXe/d02EC1/6o=", "__NEXT_PREVIEW_MODE_ID": "5b800a03180c0948551b43d7280739f7", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "64bffccf025063860cb9a4b0e4af7ae865405522205f5af539fea7752baa5a25", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8e5fbed907405ccdefb8eb794d0c58ab97e2cfcae96c0f5ab3eccd0603e4f118"}}}, "instrumentation": null, "functions": {}}